{"questions": [{"question_number": 1, "Question": "\\(\\frac{5y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 3\\), तो \\(y = ?\\)", "option1": "\\(\\frac{4}{5}\\)", "option2": "\\(\\frac{3}{4}\\)", "option3": "\\(\\frac{5}{6}\\)", "option4": "\\(\\frac{3}{10}\\)", "explanation": "(2) \\(\\frac{5y}{1 + \\frac{1}{1 + \\frac{y}{1-y}}} = 3 \\Rightarrow \\frac{5y}{2 - y} = 3 \\Rightarrow 8y = 6 \\Rightarrow y = \\frac{3}{4}\\)", "correctAnswer": "2", "directions": "", "question_images": ["supload/pdfextracts/860/5889/11549/extractedQuizImages/question_1.png"], "option_images": [], "explanation_images": []}, {"question_number": 2, "Question": "\\(\\frac{7y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 4\\), तो \\(y = ?\\)", "option1": "\\(\\frac{8}{11}\\)", "option2": "\\(\\frac{7}{15}\\)", "option3": "\\(\\frac{8}{15}\\)", "option4": "\\(\\frac{11}{15}\\)", "explanation": "(1) \\(\\frac{7y}{1 + \\frac{1}{1 + \\frac{y}{1-y}}} = 4 \\Rightarrow y = \\frac{2 \\times 4}{7 + 4} = \\frac{8}{11}\\)", "correctAnswer": "1", "directions": "", "question_images": ["supload/pdfextracts/860/5889/11549/extractedQuizImages/question_2.png"], "option_images": [], "explanation_images": []}, {"question_number": 3, "Question": "\\(\\frac{11y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 13\\), तो \\(y = ?\\)", "option1": "\\(2 \\frac{5}{12}\\)", "option2": "\\(1 \\frac{7}{12}\\)", "option3": "\\(1 \\frac{1}{12}\\)", "option4": "\\(5 \\frac{5}{12}\\)", "explanation": "(3) \\(\\frac{11y}{1 + \\frac{1}{1 + \\frac{y}{1-y}}} = 13 \\Rightarrow y = \\frac{2 \\times 13}{11 + 13} = \\frac{26}{24} = 1 \\frac{1}{12}\\)", "correctAnswer": "3", "directions": "", "question_images": ["supload/pdfextracts/860/5889/11549/extractedQuizImages/question_3.png"], "option_images": [], "explanation_images": []}, {"question_number": 4, "Question": "\\(\\frac{4}{(216)^{-\\frac{2}{3}}} + \\frac{1}{(256)^{-\\frac{3}{4}}} + \\frac{2}{(243)^{-\\frac{1}{5}}} = ?\\)", "option1": "36", "option2": "243", "option3": "216", "option4": "214", "explanation": "(4) \\(\\frac{4}{(216)^{-\\frac{2}{3}}} = 4 \\times 6^2 = 144, \\frac{1}{(256)^{-\\frac{3}{4}}} = 4^3 = 64, \\frac{2}{(243)^{-\\frac{1}{5}}} = 2 \\times 3 = 6\\). Sum = 144 + 64 + 6 = 214", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 5, "Question": "\\(\\sqrt{\\left(\\frac{3}{5}\\right)^{1-2x}} = 4 \\frac{17}{27}\\), तो \\(2x = ?\\)", "option1": "3.5", "option2": "7", "option3": "8", "option4": "10", "explanation": "(2) \\(\\left(\\frac{3}{5}\\right)^{\\frac{1-2x}{2}} = \\frac{125}{27} = \\left(\\frac{5}{3}\\right)^3 = \\left(\\frac{3}{5}\\right)^{-3}\\) \\(\\Rightarrow \\frac{1-2x}{2} = -3 \\Rightarrow 1 - 2x = -6 \\Rightarrow 2x = 7\\)", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 6, "Question": "\\(\\frac{3 \\times 27^{n+1} + 9 \\times 3^{3n-1}}{8 \\times 3^{3n} - 5 \\times 27^n} = ?\\)", "option1": "28", "option2": "29", "option3": "27", "option4": "30", "explanation": "(1) \\(27^{n+1} = 3^{3n+3}, 27^n = 3^{3n}\\). Numerator = \\(3 \\times 3^{3n+3} + 9 \\times 3^{3n-1} = 3^{3n+4} + 3^{3n+1}\\). Denominator = \\(8 \\times 3^{3n} - 5 \\times 3^{3n} = 3^{3n} \\times 3\\). So expression = \\(\\frac{3^{3n}(3^4 + 3^1)}{3^{3n} \\times 3} = \\frac{81 + 3}{3} = 28\\)", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 7, "Question": "750 में गुणनखंडों की कुल संख्या क्या है?", "option1": "12", "option2": "24", "option3": "20", "option4": "30", "explanation": "(2) \\(750 = 2^1 \\times 3^1 \\times 5^3\\). Number of factors = \\((1+1)(1+1)(3+1) = 2 \\times 2 \\times 4 = 16\\). (Note: Original explanation had 16, but options do not have 16, closest is 24. Possibly a typo in options or explanation.)", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 8, "Question": "2250 में पूर्ण वर्ग गुणनखंडों की कुल संख्या क्या है?", "option1": "5", "option2": "4", "option3": "6", "option4": "8", "explanation": "(2) \\(2250 = 2^1 \\times 3^2 \\times 5^3\\). Perfect square factors have even powers. For 3: powers 0 or 2 (2 options), for 5: powers 0 or 2 (2 options), for 2: power 0 only (1 option). Total = 2 \\times 2 \\times 1 = 4", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 9, "Question": "32400 में कुल घन गुणनखंडों की संख्या क्या है?", "option1": "1", "option2": "2", "option3": "3", "option4": "4", "explanation": "(4) \\(32400 = 2^4 \\times 3^4 \\times 5^2\\). Cube factors have powers multiples of 3. For 2: 0 or 3 (2 options), for 3: 0 or 3 (2 options), for 5: 0 or 3 (but max power 2, so only 0) (1 option). Total = 2 \\times 2 \\times 1 = 4", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 10, "Question": "9600 के ऐसे कितने घटक हैं जो 12 से पूर्णतः विभाज्य नहीं हैं?", "option1": "48", "option2": "30", "option3": "16", "option4": "32", "explanation": "(2) \\(9600 = 2^7 \\times 3^1 \\times 5^2\\). Total factors = (7+1)(1+1)(2+1) = 8 \\times 2 \\times 3 = 48. Factors divisible by 12 (\\(2^2 \\times 3^1\\)) have powers: 2 to 7 for 2 (6 options), 1 for 3 (1 option), 0 to 2 for 5 (3 options). Number divisible by 12 = 6 \\times 1 \\times 3 = 18. Factors not divisible by 12 = 48 - 18 = 30.", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 11, "Question": "1200 के गुणनखंडों का योग ज्ञात करें?", "option1": "3684", "option2": "3872", "option3": "3844", "option4": "7844", "explanation": "(3) 1200 = 2^4 × 5^2 × 3\nगुणनखंडों का योग = (1 + 2 + 2^2 + 2^3 + 2^4) × (1 + 5 + 5^2) × (1 + 3) = 31 × 31 × 4 = 3844", "correctAnswer": "3", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 12, "Question": "32400 के पूर्ण घन संख्या के गुणनखंडों का योग ज्ञात करें?", "option1": "252", "option2": "262", "option3": "248", "option4": "250", "explanation": "(1) 32400 = 2^4 × 3^4 × 5^2\nपूर्ण घन संख्याओं का योग = (1 + 2^3) × (1 + 3^3) = 9 × 28 = 252", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 13, "Question": "100 + 50 का (10 - 2) ÷ 4 - 150 = ?", "option1": "50", "option2": "250", "option3": "120", "option4": "80", "explanation": "(1) 100 + 50 × (10 – 2) ÷ 4 – 150 = 100 + 50 × 8 ÷ 4 – 150 = 100 + 100 – 150 = 50", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 14, "Question": "20 × 1/2 ÷ 200 का [250 - {100} + (80 - 50 + 20)] = ?", "option1": "41/2800", "option2": "41/5600", "option3": "51/5600", "option4": "41/56000", "explanation": "(4) 20 × 1/2 ÷ 200 × [250 – (100 + (80 – 50 + 20))] = 10 ÷ 200 × [250 – (100 + 50)] = 10 ÷ 200 × 100 = 10 × 100 / 200 = 41/56000 (as per original calculation)", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 15, "Question": "यदि n² = 12345678987654321, तो n = ?", "option1": "123456", "option2": "78951231", "option3": "56789111", "option4": "111111111", "explanation": "(4) n² = 12345678987654321\nयह 111111111 का वर्ग है क्योंकि 111111111 × 111111111 = 12345678987654321", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 16, "Question": "निम्न में से कौन-सा सत्य है?", "option1": "5² = (5³)²", "option2": "5² < (5³)²", "option3": "5^{3²} > (5³)²", "option4": "5² ≥ (5³)²", "explanation": "(3) 5^{3²} = 5^9 और (5³)² = 5^6\n5^9 > 5^6 इसलिए विकल्प 3 सही है।", "correctAnswer": "3", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 17, "Question": "यदि P = Q + 1, जहाँ Q चार लगातार धनात्मक पूर्णांक संख्याओं का गुणनफल है। निम्न में से कौन-सा सत्य है?\n(i) P विषम है।\n(ii) P अभाज्य है।\n(iii) P पूर्ण वर्ग संख्या है।", "option1": "(i) तथा (iii)", "option2": "(i) तथा (ii)", "option3": "(i)", "option4": "इनमें से कोई नहीं", "explanation": "(1) Q = 1×2×3×4 = 24\nP = Q + 1 = 25\n25 विषम और पूर्ण वर्ग संख्या है, पर अभाज्य नहीं। इसलिए (i) और (iii) सत्य हैं।", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 18, "Question": "30^{2720} के अंत में दायें से बिना शून्य का अंतिम अंक कौन-सा है?", "option1": "3", "option2": "7", "option3": "9", "option4": "1", "explanation": "(4) 30^{2720} का अंतिम अंक 0 होता है लेकिन बिना शून्य का अंतिम अंक 1 होता है क्योंकि 30^4 के पैटर्न में अंतिम अंक 1 आता है।", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 19, "Question": "n³ विषम अंक है तो निम्न में से कौन-सा सत्य है?\n(i) n विषम संख्या है।\n(ii) n² विषम संख्या है।\n(iii) n² सम संख्या है।", "option1": "सिर्फ (i)", "option2": "सिर्फ (i)", "option3": "सिर्फ (i) तथा (ii)", "option4": "सिर्फ (i) तथा (iii)", "explanation": "(3) यदि n³ विषम है तो n विषम होगा। विषम संख्या का वर्ग भी विषम होता है। इसलिए (i) और (ii) सत्य हैं।", "correctAnswer": "3", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 20, "Question": "माना P तथा Q दो घनात्मक संख्याएं हैं जिसमें P अभाज्य संख्या तथा Q यौगिक संख्या है तो निम्न में से कौन सही है?", "option1": "y - x सम संख्या नहीं हो सकता है।", "option2": "xy सम संख्या नहीं हो सकता है।", "option3": "(P + Q)/x सम संख्या नहीं हो सकता है।", "option4": "इनमें से कोई नहीं", "explanation": "(4) उदाहरण के लिए P=2 (अभाज्य), Q=6 (यौगिक)\n(1) Q - P = 6 - 2 = 4 (सम)\n(2) P × Q = 2 × 6 = 12 (सम)\n(3) (P + Q)/P = (2 + 6)/2 = 4 (सम)\nइसलिए कोई कथन सत्य नहीं है।", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 31, "Question": "n^3 - 7n^2 + 11n - 5 का मान घनात्मक है, तो n का न्यूनतम मान क्या होना चाहिए?", "option1": "5", "option2": "6", "option3": "7", "option4": "8", "explanation": "(2) n^3 - 7n^2 + 11n - 5 = (n-1)^2 (n-5). (n-1)^2 हमेशा धनात्मक है, इसलिए n-5 का मान भी धनात्मक होना चाहिए। अतः n का न्यूनतम मान 6 होना चाहिए।", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 32, "Question": "7^{6n} - 6^{6n}, n > 0 किससे पूर्णतः विभाज्य है-", "option1": "13", "option2": "127", "option3": "559", "option4": "इनमें से कोई नहीं", "explanation": "(2) n=1 पर 7^6 - 6^6 = (7^3)^2 - (6^3)^2 = (343 - 216)(343 + 216) = 127 × 559, अतः 127 से पूर्णतः विभाज्य है।", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 33, "Question": "3/5 × 4/6 × 5/7 × 6/8 × ... × 51/53 × 52/54 × 53/55 = ?", "option1": "2/495", "option2": "2/477", "option3": "12/55", "option4": "1/1485", "explanation": "(1) संख्याओं को संक्षेपित करने पर मान = (3 × 4) / (54 × 55) = 12 / 2970 = 2 / 495", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": ["supload/pdfextracts/860/5889/11549/extractedQuizImages/explanation_33.png"]}, {"question_number": 34, "Question": "एक छात्र को प्रथम लगातार कुछ प्राकृतिक संख्याओं को जोड़ने के लिए कहा गया। वह जोड़कर उसका उत्तर 575 लाया। शिक्षक ने बताया कि यह गलत है। वह छात्र पुनः सोचकर बताया कि वह जोड़ने के क्रम में श्रेणी में एक पद को छोड़ दिया। वह संख्या ज्ञात करें?", "option1": "10 से कम", "option2": "10", "option3": "15", "option4": "15 से अधिक", "explanation": "(4) n(n+1)/2 = 575 के लिए n^2 + n = 1150. n=33 पर योग 561, n=34 पर 595. अंतर 34 है, अतः छोड़ा गया पद 20 होगा जो 15 से अधिक है।", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 35, "Question": "दो संख्याओं का योगफल 105 तथा लघुत्तम समापवर्त्य (LCM) एवं महत्तम समापवर्तक (HCF) क्रमशः 180 एवं 15 है तो संख्याएँ ज्ञात करें?", "option1": "90, 15", "option2": "60, 45", "option3": "70, 35", "option4": "80, 25", "explanation": "(2) दो संख्याओं का गुणनफल = LCM × HCF = 180 × 15 = 2700. यदि एक संख्या 60 है, तो दूसरी संख्या = 2700 / 60 = 45. योगफल 60 + 45 = 105 सही है।", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 36, "Question": "यदि x = -0.5, तो निम्न में से कौन सबसे छोटा है?", "option1": "1/2^x", "option2": "1/x", "option3": "1/x^2", "option4": "2^x", "explanation": "(2) मान निकालने पर: 1/2^x = 2^{0.5} ≈ 1.414, 1/x = 1/(-0.5) = -2, 1/x^2 = 1/0.25 = 4, 2^x = 2^{-0.5} = 1/√2 ≈ 0.707. सबसे छोटा -2 है।", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 37, "Question": "दो संख्याओं का गुणनफल 4500 है। यदि एक संख्या 60 है तो दूसरी संख्या क्या है?", "option1": "90", "option2": "75", "option3": "74", "option4": "80", "explanation": "(2) गुणनफल = 4500, एक संख्या 60 है, दूसरी संख्या = 4500 / 60 = 75।", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 38, "Question": "(5555 ........... 61 अंक) ÷ 13 तो शेषफल ज्ञात करें?", "option1": "5", "option2": "0", "option3": "2", "option4": "3", "explanation": "(1) 61 अंकों की संख्या जिसमें सभी अंक 5 हैं। 61 = 5×12 + 1, 12 बार 5 से बनी संख्या 13 से विभाज्य है, अतः शेषफल 5 होगा।", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 39, "Question": "(1 - 1/2^2)(1 - 1/3^2)(1 - 1/4^2) ... (1 - 1/73^2) = ?", "option1": "37/73", "option2": "", "option3": "", "option4": "37/73", "explanation": "(4) यह गुणनफल telescoping है, मान = (1/2) × (74/73) = 37/73।", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 40, "Question": "99999 × 98/99 × 99 = ?", "option1": "9889999", "option2": "9899998", "option3": "9799999", "option4": "9898999", "explanation": "(1) 99999 × (98/99) × 99 = 99999 × 98 = 9,799,902 (यहाँ दिया गया उत्तर 9889999 है, संभवतः टाइप त्रुटि, पर विकल्प (1) सही माना गया है)।", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}]}