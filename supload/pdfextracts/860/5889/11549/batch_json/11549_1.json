{"questions": [{"question_number": 1, "Question": "\\(\\frac{5y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 3\\), तो \\(y = ?\\)", "option1": "\\(\\frac{4}{5}\\)", "option2": "\\(\\frac{3}{4}\\)", "option3": "\\(\\frac{5}{6}\\)", "option4": "\\(\\frac{3}{10}\\)", "explanation": "(2) \\(\\frac{5y}{1 + \\frac{1}{1 + \\frac{y}{1-y}}} = 3 \\Rightarrow \\frac{5y}{2 - y} = 3 \\Rightarrow 8y = 6 \\Rightarrow y = \\frac{3}{4}\\)", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 2, "Question": "\\(\\frac{7y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 4\\), तो \\(y = ?\\)", "option1": "\\(\\frac{8}{11}\\)", "option2": "\\(\\frac{7}{15}\\)", "option3": "\\(\\frac{8}{15}\\)", "option4": "\\(\\frac{11}{15}\\)", "explanation": "(1) \\(\\frac{7y}{1 + \\frac{1}{1 + \\frac{y}{1-y}}} = 4 \\Rightarrow y = \\frac{2 \\times 4}{7 + 4} = \\frac{8}{11}\\)", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 3, "Question": "\\(\\frac{11y}{1+\\frac{1}{1+\\frac{y}{1-y}}} = 13\\), तो \\(y = ?\\)", "option1": "\\(2 \\frac{5}{12}\\)", "option2": "\\(1 \\frac{7}{12}\\)", "option3": "\\(1 \\frac{1}{12}\\)", "option4": "\\(5 \\frac{5}{12}\\)", "explanation": "(3) \\(\\frac{11y}{1 + \\frac{1}{1 + \\frac{y}{1-y}}} = 13 \\Rightarrow y = \\frac{2 \\times 13}{11 + 13} = \\frac{26}{24} = 1 \\frac{1}{12}\\)", "correctAnswer": "3", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 4, "Question": "\\(\\frac{4}{(216)^{-\\frac{2}{3}}} + \\frac{1}{(256)^{-\\frac{3}{4}}} + \\frac{2}{(243)^{-\\frac{1}{5}}} = ?\\)", "option1": "36", "option2": "243", "option3": "216", "option4": "214", "explanation": "(4) \\(\\frac{4}{(216)^{-\\frac{2}{3}}} = 4 \\times 6^2 = 144, \\frac{1}{(256)^{-\\frac{3}{4}}} = 4^3 = 64, \\frac{2}{(243)^{-\\frac{1}{5}}} = 2 \\times 3 = 6\\). Sum = 144 + 64 + 6 = 214", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 5, "Question": "\\(\\sqrt{\\left(\\frac{3}{5}\\right)^{1-2x}} = 4 \\frac{17}{27}\\), तो \\(2x = ?\\)", "option1": "3.5", "option2": "7", "option3": "8", "option4": "10", "explanation": "(2) \\(\\left(\\frac{3}{5}\\right)^{\\frac{1-2x}{2}} = \\frac{125}{27} = \\left(\\frac{5}{3}\\right)^3 = \\left(\\frac{3}{5}\\right)^{-3}\\) \\(\\Rightarrow \\frac{1-2x}{2} = -3 \\Rightarrow 1 - 2x = -6 \\Rightarrow 2x = 7\\)", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 6, "Question": "\\(\\frac{3 \\times 27^{n+1} + 9 \\times 3^{3n-1}}{8 \\times 3^{3n} - 5 \\times 27^n} = ?\\)", "option1": "28", "option2": "29", "option3": "27", "option4": "30", "explanation": "(1) \\(27^{n+1} = 3^{3n+3}, 27^n = 3^{3n}\\). Numerator = \\(3 \\times 3^{3n+3} + 9 \\times 3^{3n-1} = 3^{3n+4} + 3^{3n+1}\\). Denominator = \\(8 \\times 3^{3n} - 5 \\times 3^{3n} = 3^{3n} \\times 3\\). So expression = \\(\\frac{3^{3n}(3^4 + 3^1)}{3^{3n} \\times 3} = \\frac{81 + 3}{3} = 28\\)", "correctAnswer": "1", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 7, "Question": "750 में गुणनखंडों की कुल संख्या क्या है?", "option1": "12", "option2": "24", "option3": "20", "option4": "30", "explanation": "(2) \\(750 = 2^1 \\times 3^1 \\times 5^3\\). Number of factors = \\((1+1)(1+1)(3+1) = 2 \\times 2 \\times 4 = 16\\). (Note: Original explanation had 16, but options do not have 16, closest is 24. Possibly a typo in options or explanation.)", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 8, "Question": "2250 में पूर्ण वर्ग गुणनखंडों की कुल संख्या क्या है?", "option1": "5", "option2": "4", "option3": "6", "option4": "8", "explanation": "(2) \\(2250 = 2^1 \\times 3^2 \\times 5^3\\). Perfect square factors have even powers. For 3: powers 0 or 2 (2 options), for 5: powers 0 or 2 (2 options), for 2: power 0 only (1 option). Total = 2 \\times 2 \\times 1 = 4", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 9, "Question": "32400 में कुल घन गुणनखंडों की संख्या क्या है?", "option1": "1", "option2": "2", "option3": "3", "option4": "4", "explanation": "(4) \\(32400 = 2^4 \\times 3^4 \\times 5^2\\). Cube factors have powers multiples of 3. For 2: 0 or 3 (2 options), for 3: 0 or 3 (2 options), for 5: 0 or 3 (but max power 2, so only 0) (1 option). Total = 2 \\times 2 \\times 1 = 4", "correctAnswer": "4", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}, {"question_number": 10, "Question": "9600 के ऐसे कितने घटक हैं जो 12 से पूर्णतः विभाज्य नहीं हैं?", "option1": "48", "option2": "30", "option3": "16", "option4": "32", "explanation": "(2) \\(9600 = 2^7 \\times 3^1 \\times 5^2\\). Total factors = (7+1)(1+1)(2+1) = 8 \\times 2 \\times 3 = 48. Factors divisible by 12 (\\(2^2 \\times 3^1\\)) have powers: 2 to 7 for 2 (6 options), 1 for 3 (1 option), 0 to 2 for 5 (3 options). Number divisible by 12 = 6 \\times 1 \\times 3 = 18. Factors not divisible by 12 = 48 - 18 = 30.", "correctAnswer": "2", "directions": "", "question_images": [], "option_images": [], "explanation_images": []}]}